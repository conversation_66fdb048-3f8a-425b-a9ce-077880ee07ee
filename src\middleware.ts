import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { getToken } from "next-auth/jwt"

export async function middleware(req: NextRequest) {
    const token = await getToken({ req }) // Lấy token của NextAuth
    
    const { pathname } = req.nextUrl

    // Nếu người dùng đã đăng nhập và đang ở trang login → chuyển hướng sang home
    if (token && pathname === "/login") {
        return NextResponse.redirect(new URL("/", req.url))
    }

    // Nếu chưa đăng nhập và vào trang cần bảo vệ → chuyển hướng đến login
    if (!token && !pathname.startsWith("/login")) {
        return NextResponse.redirect(new URL("/login", req.url))
    }

    return NextResponse.next() // Cho phép tiếp tục truy cập
}

// Áp dụng middleware cho tất cả route trừ các file tĩnh và thư mục public
export const config = {
    matcher: ["/((?!api|public|_next/static|_next/image|.*\\.png$).*)"],
}
