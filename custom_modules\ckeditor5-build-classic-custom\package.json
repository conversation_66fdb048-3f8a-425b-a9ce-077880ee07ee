{"name": "ckeditor5-custom-build", "author": "CKSource", "description": "A custom CKEditor 5 build made by the CKEditor 5 online builder.", "version": "0.0.1", "license": "SEE LICENSE IN LICENSE.md", "private": true, "main": "./build/ckeditor.js", "devDependencies": {"@ckeditor/ckeditor5-basic-styles": "^35.4.0", "@ckeditor/ckeditor5-dev-utils": "^30.5.0", "@ckeditor/ckeditor5-dev-webpack-plugin": "^30.5.0", "@ckeditor/ckeditor5-editor-classic": "^35.4.0", "@ckeditor/ckeditor5-essentials": "^35.4.0", "@ckeditor/ckeditor5-heading": "^35.4.0", "@ckeditor/ckeditor5-image": "^35.4.0", "@ckeditor/ckeditor5-link": "^35.4.0", "@ckeditor/ckeditor5-list": "^35.4.0", "@ckeditor/ckeditor5-media-embed": "^35.4.0", "@ckeditor/ckeditor5-paragraph": "^35.4.0", "@ckeditor/ckeditor5-table": "^35.4.0", "@ckeditor/ckeditor5-theme-lark": "^35.4.0", "@ckeditor/ckeditor5-typing": "^35.4.0", "@ckeditor/ckeditor5-upload": "^35.4.0", "@ckeditor/ckeditor5-word-count": "^35.4.0", "css-loader": "^5.2.7", "postcss": "^8.4.20", "postcss-loader": "^4.3.0", "raw-loader": "^4.0.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^4.2.3", "webpack": "^5.75.0", "webpack-cli": "^4.10.0"}, "scripts": {"build": "webpack --mode production"}}