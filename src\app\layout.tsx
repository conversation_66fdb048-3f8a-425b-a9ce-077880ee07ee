import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono, Inter } from "next/font/google";
import "./globals.css";
import { AntdRegistry } from '@ant-design/nextjs-registry';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});
import { ConfigProvider } from "antd";
import { NotificationProvider } from "@/ultils/provider/NotificationProvider";

const inter = Inter({ subsets: ['latin'] })


export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.className}  antialiased w-screen h-screen`}
      ><NotificationProvider>
          <AntdRegistry>
            <ConfigProvider
              theme={{
                token: {
                  colorPrimary: "#046C4E", // Màu primary của bạn
                },
              }}
            >{children}</ConfigProvider>
          </AntdRegistry>
        </NotificationProvider>
      </body>
    </html>
  );
}
