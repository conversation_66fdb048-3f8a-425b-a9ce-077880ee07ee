import useSWR, { mutate, SWRConfiguration } from "swr";
import { useEffect } from "react";
import { useNotification } from "@/ultils/provider/NotificationProvider";

export function useCustomSWR<Data = any, Error = any>(
  key: string | any[],
  fetcher: (url: string) => Promise<Data>,
  options?: SWRConfiguration
) {
  const { showError } = useNotification(); // Hook để hiển thị thông báo lỗi

  const defaultOptions: SWRConfiguration = {
    errorRetryCount: 3, // Retry tối đa 3 lần
    errorRetryInterval: 15000, // Mỗi lần retry cách nhau 15 giây
    shouldRetryOnError: (err) => err.status !== 404, // Chỉ retry nếu lỗi KHÔNG phải 404
    revalidateOnFocus: false, // Không fetch lại khi focus vào trang
    revalidateIfStale: false, // Không tự động re-fetch nếu dữ liệu cũ
    revalidateOnReconnect: true, // Fetch lại khi mạng được kết nối
  };

  const { data, error, isLoading } = useSWR<Data, Error>(key, fetcher, { ...defaultOptions, ...options });

  // Hiển thị thông báo lỗi khi có lỗi API
  useEffect(() => {
    if (error) {
      showError(`${error || "Không xác định"}`);
    }
  }, [error, showError]);

  return {
    data: data ?? [],
    isLoading,
    isError: !!error,
    refresh: () => mutate(key),
  };
}

/** Hàm để refresh dữ liệu theo key */
export function refreshData(key: string) {
  mutate(key);
}
