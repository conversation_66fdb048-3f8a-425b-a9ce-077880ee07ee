(function(e){const a=e["tr"]=e["tr"]||{};a.dictionary=Object.assign(a.dictionary||{},{"%0 of %1":"%0/%1",Aquamarine:"<PERSON> Yeşili",Black:"Siyah",Blue:"<PERSON>vi",Bold:"<PERSON><PERSON><PERSON><PERSON>","Bulleted List":"Simgeli Liste",Cancel:"İptal","Change image text alternative":"Görsel alternatif yazısını değiştir","Characters: %0":"Karakterler: %0","Choose heading":"Başlık tipi seç",Column:"Kolon","Delete column":"<PERSON>lonu sil","Delete row":"Satırı sil","Dim grey":"Koyu Gri",Downloadable:"İndirilebilir","Dropdown toolbar":"Açılır araç çubuğu","Edit block":"Bloğu Düzenle","Edit link":"Bağlantıyı değiştir","Editor block content toolbar":"Düzenleyici engelleme içerik araç çubuğu","Editor contextual toolbar":"Düzenleyici içeriksel araç çubuğu","Editor editing area: %0":"Editör düzenleme alanı: %0","Editor toolbar":"Düzenleme araç çubuğu",Green:"Yeşil",Grey:"Gri","Header column":"Başlık kolonu","Header row":"Başlık satırı",Heading:"Başlık","Heading 1":"1. Seviye Başlık","Heading 2":"2. Seviye Başlık","Heading 3":"3. Seviye Başlık","Heading 4":"4. Seviye Başlık","Heading 5":"5. Seviye Başlık","Heading 6":"6. Seviye Başlık","image widget":"resim aracı","Insert column left":"Sola kolon ekle","Insert column right":"Sağa kolon ekle","Insert image":"Görsel Ekle","Insert media":"Medya Ekle","Insert paragraph after block":"Bloktan sonra paragraf ekle","Insert paragraph before block":"Bloktan önce paragraf ekle","Insert row above":"Üste satır ekle","Insert row below":"Alta satır ekle","Insert table":"Tablo Ekle",Italic:"İtalik","Light blue":"Açık Mavi","Light green":"Açık Yeşil","Light grey":"Açık Gri",Link:"Bağlantı","Link URL":"Bağlantı Adresi","Media URL":"Medya URL'si","media widget":"medya aracı","Merge cell down":"Aşağıya doğru birleştir","Merge cell left":"Sola doğru birleştir","Merge cell right":"Sağa doğru birleştir","Merge cell up":"Yukarı doğru birleştir","Merge cells":"Hücreleri birleştir",Next:"Sonraki","Numbered List":"Numaralı Liste","Open in a new tab":"Yeni sekmede aç","Open link in new tab":"Yeni sekmede aç","Open media in new tab":"Medyayı yeni sekmede aç",Orange:"Turuncu",Paragraph:"Paragraf","Paste the media URL in the input.":"Medya URL'siini metin kutusuna yapıştırınız.","Press Enter to type after or press Shift + Enter to type before the widget":"Görsel bileşenden sonra yazmak için Enter'a basın ya da görsel bileşenden önce yazmak için Shift + Enter'a basın",Previous:"Önceki",Purple:"Mor",Red:"Kırmızı",Redo:"Tekrar yap","Rich Text Editor":"Zengin İçerik Editörü",Row:"Satır",Save:"Kaydet","Select all":"Hepsini seç","Select column":"Kolon seç","Select row":"Satır seç","Show more items":"Daha fazla öğe göster","Split cell horizontally":"Hücreyi yatay böl","Split cell vertically":"Hücreyi dikey böl",Strikethrough:"Üstü çizili","Table toolbar":"Tablo araç çubuğu","Text alternative":"Yazı alternatifi","The URL must not be empty.":"URL boş olamaz.","This link has no URL":"Bağlantı adresi yok","This media URL is not supported.":"Desteklenmeyen Medya URL'si.","Tip: Paste the URL into the content to embed faster.":"İpucu: İçeriği daha hızlı yerleştirmek için URL'yi yapıştırın.",Turquoise:"Turkuaz",Underline:"Altı Çizgili",Undo:"Geri al",Unlink:"Bağlantıyı kaldır","Upload failed":"Yükleme başarsız","Upload in progress":"Yükleme işlemi devam ediyor",White:"Beyaz","Widget toolbar":"Bileşen araç çubuğu","Words: %0":"Kelimeler: %0",Yellow:"Sarı"});a.getPluralForm=function(e){return e>1}})(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));