import { LichHop } from "../type/api/LichHop";
import { apiClient } from "./api-client";
import { ApiUrl } from "../ultils/apiUrl";

export function getLichHop(params?: Partial<any>): Promise<LichHop[]> {
  const stationKeyMap: Record<string, string> = {
    stationName: "station_name",
    stationTypeId: "station_type_id",
    provinceId: "province_id",
    areaId: "area_id",
  };
//   const queryParams = new URLSearchParams(mapRequestParams(params || {}, stationKeyMap) as any).toString();
  // const queryString = new URLSearchParams(params as Record<string, string>).toString();
  // console.log(queryString);
  return apiClient<LichHop[]>(`/${ApiUrl.lichHop}`);
}