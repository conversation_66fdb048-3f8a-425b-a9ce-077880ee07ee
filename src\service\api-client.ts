import { AppResponseType } from "../type/response";
import { ApiUrl } from "../ultils/apiUrl";
import { getSession } from "next-auth/react";


export async function apiClient<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const API_URL = ApiUrl.baseUrl;

    // Lấy session để lấy access_token
    const session = await getSession();
    console.log(session)
    const accessToken = session?.accessToken || "";
    console.log(accessToken)
    const res = await fetch(`${API_URL}${endpoint}`, {
        headers: {
            "Content-Type": "application/json",
            "Authorization": `bearer ${accessToken}`, // Truyền token vào header
            ...(options.headers || {}),
        },
        ...options,
    });

    if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData?.message || "Something went wrong");
    }

    const responseData: AppResponseType<T> = await res.json();

    // Kiểm tra nếu status !== 1 thì ném lỗi
    if (responseData?.error !== 0) {
        throw new Error(responseData?.message || "API response status is not 1");
    }

    return responseData.data; // Chỉ trả về phần `data`

}
