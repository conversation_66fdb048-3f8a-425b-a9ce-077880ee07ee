import Image from "next/image";
import Link from "next/link";
import HomeHeader from "./HomeHeader";

interface ItemType {
    image: string;
    title: string;
    color: string;
    route: string;

}

const ItemData: ItemType[] = [
    {
        image: '/images/icons/monitor-recorder.png',
        title: 'Ch<PERSON>ơng trình họp',
        color: '#E4F5E4',
        route : '/chuong-trinh-hop'
    }, {
        image: '/images/icons/document-text.png',
        title: 'T<PERSON><PERSON> liệu họp',
        color: '#DDEFFD',
        route : 'tai-lieu-hop'
    }, {
        image: '/images/icons/notification-bing.png',
        title: 'Đăng ký phát biểu Tranh luận',
        color: '#FAE0DF',
        route : '/dang-ky-phat-bieu/register'
    }, {
        image: '/images/icons/directbox-receive.png',
        title: 'V<PERSON> trí ngồi',
        color: '#FFEFD8',
        route : ''
    }, {
        image: '/images/icons/like.png',
        title: '<PERSON><PERSON><PERSON><PERSON> quyết',
        color: '#E9E4F5',
        route : ''
    }, {
        image: '/images/icons/profile-2user.png',
        title: '<PERSON><PERSON> s<PERSON>ch đại biểu',
        color: '#DDEFFD',
        route : ''
    }, {
        image: '/images/icons/chart.png',
        title: 'Phê duyệt báo cáo',
        color: '#D5F5F6',
        route : ''
    },
]

const HomePage = () => {
    return (
        <>
            <div className="bg-gradient-to-r from-[#014A3F] to-[#41754F] h-full w-full flex flex-col">
                <HomeHeader />
                <div className="flex-1  w-full bg-[#F3F4F6] rounded-t-[36px] text-black px-8 py-10">
                    <section className="flex justify-start h-fit">
                        <div className="px-3">
                            <h3 className="font-bold text-[48px] text-[#047481] leading-none ">26</h3>
                            <p>Tháng 8</p>
                        </div>
                        <div className="h-auto w-[1px] bg-[#046C4E] mx-8">

                        </div>
                        <div>
                            <h3 className="font-medium text-[18px]">Triển khai kế hoạch Tháng 10 và Đại hội Đảng các cấp - 2025</h3>
                            <div className="flex justify-start items-center">
                                <Image className="inline" alt="Back" width={18} height={18} src={'/images/icons/note.png'} />
                                <p className="ms-3"><b>08:30 - 12:00</b> Ngày 26/08/2025</p>
                            </div>
                            <div className="flex justify-start items-center">
                                <Image className="inline" alt="Back" width={18} height={18} src={'/images/icons/location.png'} />
                                <p className="ms-3">Phòng số 2 - Bộ Công An</p>
                            </div>


                        </div>
                    </section>
                    <section className="grid grid-cols-3 gap-4 mt-16">
                        {
                            ItemData.map((data, index) => {
                                return <Item data={data} key={index} />
                            })
                        }

                    </section>
                </div>
            </div>
        </>
    );

}

const Item = ({ data }: { data: ItemType }) => {
    return (
        <>
            <Link href={data.route} className="w-full aspect-square rounded-[22px] shadow-[0px_2px_4px_-2px_rgba(0,0,0,0.05),0px_4px_6px_-1px_rgba(0,0,0,0.1)] flex flex-col items-center justify-center bg-white">

                <div className={`rounded-[32px] w-1/2 aspect-square flex justify-center items-center`} style={{ background: data.color }}>
                    <Image className="w-1/3" sizes="100%" priority alt="Back" width={0} height={0} src={data.image} />
                </div>

                <p className="mt-5 font-[500]">
                    {data.title}
                </p>

            </Link>
        </>
    );
}

export default HomePage;  