import Image from "next/image";

export default function Ticket() {
    return (<>
        <section>
            <div className="border-b-1 border-[#E5E7EB] py-8">
                <div className="flex justify-between">
                    <h3 className="font-bold">
                        <PERSON><PERSON><PERSON><PERSON>
                    </h3>
                    <span className="font-extralight">
                        Góp ý ngày: 20/08/2025
                    </span>
                </div>
                <p className="mt-3">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur omnis, eum doloremque nam modi adipisci cumque, ut perferendis commodi aliquam molestias nostrum fugit repellat incidunt beatae quas placeat, numquam cum!
                </p>
                <div className="flex justify-start items-center mt-5">
                    <Image className="w-[32px]" sizes="100%" priority alt="Back" width={0} height={0} src={`/images/icons/pdf_icon.png`} />
                    <div className="ms-3">
                        <p>T<PERSON><PERSON> liệu chuyển đổi số - <PERSON><PERSON><PERSON><PERSON> bản họp kế hoạch tháng 10</p>

                    </div>
                </div>
            </div>

            <div className="border-b-1 border-[#E5E7EB] py-8">
                <div className="flex justify-between">
                    <h3 className="font-bold">
                        Nguyễn Khánh An
                    </h3>
                    <span className="font-extralight">
                        Góp ý ngày: 20/08/2025
                    </span>
                </div>
                <p className="mt-3">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur omnis, eum doloremque nam modi adipisci cumque, ut perferendis commodi aliquam molestias nostrum fugit repellat incidunt beatae quas placeat, numquam cum!
                </p>
                <div className="flex justify-start items-center mt-5">
                    <Image className="w-[32px]" sizes="100%" priority alt="Back" width={0} height={0} src={`/images/icons/word_icon.png`} />
                    <div className="ms-3">
                        <p>Tài liệu chuyển đổi số - Biên bản họp kế hoạch tháng 10</p>

                    </div>
                </div>
            </div>

            <div className="border-b-1 border-[#E5E7EB] py-8">
                <div className="flex justify-between">
                    <h3 className="font-bold">
                        Nguyễn Khánh An
                    </h3>
                    <span className="font-extralight">
                        Góp ý ngày: 20/08/2025
                    </span>
                </div>
                <p className="mt-3">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit. Aspernatur omnis, eum doloremque nam modi adipisci cumque, ut perferendis commodi aliquam molestias nostrum fugit repellat incidunt beatae quas placeat, numquam cum!
                </p>
                <div className="flex justify-start items-center mt-5">
                    <Image className="w-[32px]" sizes="100%" priority alt="Back" width={0} height={0} src={`/images/icons/excel_icon.png`} />
                    <div className="ms-3">
                        <p>Tài liệu chuyển đổi số - Biên bản họp kế hoạch tháng 10</p>

                    </div>
                </div>
            </div>
        </section>

    </>);
}