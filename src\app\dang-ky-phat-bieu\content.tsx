"use client"
import { Button, ConfigProvider, Form, Input, Tabs, DatePicker } from 'antd';
import type { FormProps, TabsProps } from 'antd';

import Image from 'next/image';
import { useState } from 'react';
import Link from 'next/link';
import <PERSON>agger from 'antd/es/upload/Dragger';
import ChoPhat<PERSON>ieu from './ChoPhatBieu';
import DaPhatBieu from './DaPhatBieu';


const items: TabsProps['items'] = [
    {
        key: '1',
        label: 'Chờ phát biểu',
        children: <ChoPhatBieu />,
    },
    {
        key: '2',
        label: 'Đã phát biểu',
        children: <DaPhatBieu />,
    },

];

type FieldType = {
    username?: string;
    password?: string;
    remember?: string;
};

const onFinish: FormProps<FieldType>['onFinish'] = (values) => {
    console.log('Success:', values);
};

const onFinishFailed: FormProps<FieldType>['onFinishFailed'] = (errorInfo) => {
    console.log('Failed:', errorInfo);
};

export default function Content() {
    const [isForm, setIsForm] = useState<boolean>(false)

    return (
        <><ConfigProvider
            theme={{
                components: {
                    Tabs: {
                        /* here is your component tokens */
                        // horizontalItemPadding : '12px 20px',


                    },
                },
            }}
        >
            <Tabs defaultActiveKey="1" items={items}  />

            {isForm ? <div className="fixed w-screen h-screen top-0 left-0 bg-black/50 text-black">
                <div className="flex w-full h-full justify-center items-center ">
                    <div className="w-[550px]  bg-white  px-6 py-6 rounded-3xl text-center relative">
                        <div className="">
                            <div className='relative '>

                                <h3 className="font-bold text-center text-[20px] my-4">
                                    Tải lên tài liệu cuộc họp
                                </h3>
                                <div className="absolute top-0 right-0">
                                    <button onClick={() => setIsForm(false)}>
                                        <Image alt="Logo" src={`/images/icons/close.png`} width={28} height={28} />

                                    </button>
                                </div>
                            </div>

                            <Form
                                name="basic"
                                layout='vertical'
                                style={{ width: '100%', marginTop: 24 }}
                                initialValues={{ remember: true }}
                                onFinish={onFinish}
                                onFinishFailed={onFinishFailed}
                                autoComplete="off"
                            >
                                <Form.Item<FieldType>

                                    rules={[{ required: true, message: 'Please input your username!' }]}
                                    label={`Tên tài liệu`}
                                >
                                    <Input size="large" style={{ height: 52 }} placeholder="Nhập tên tài liệu" />

                                </Form.Item>

                                <Form.Item<FieldType>
                                    rules={[{ required: true, message: 'Please input your username!' }]}
                                    label={`Ngày tải lên`}
                                >
                                    <DatePicker style={{ width: "100%", height: 52 }} placeholder='Ngày tải lên' />

                                </Form.Item>
                                <Form.Item<FieldType>
                                    rules={[{ required: true, message: 'Please input your username!' }]}
                                    label={`Ngày tải lên`}
                                >
                                    <Dragger>
                                        <p className="ant-upload-drag-icon flex justify-center">
                                            <Image alt="Upload" width={60} height={60} src={`/images/icons/upload_frame.png`} />

                                        </p>
                                        <p className="ant-upload-text">Files supported: JPG, PNG, GIF</p>

                                    </Dragger>
                                </Form.Item>




                                <div className='grid grid-cols-2 gap-5'>
                                    {/* <Form.Item label={null} > */}
                                    <Button type="primary" onClick={() => setIsForm(false)} className='w-full !text-black !bg-[#D1D5DB]' style={{
                                        paddingTop: 30,
                                        paddingBottom: 30,
                                        fontWeight: 'bold'

                                    }}>
                                        Hủy bỏ
                                    </Button>
                                    {/* </Form.Item> */}
                                    <Form.Item label={null} >
                                        <Button type="primary" htmlType="submit" className='w-full' style={{
                                            paddingTop: 30,
                                            paddingBottom: 30,
                                            fontWeight: 'bold'

                                        }}>
                                            Tải lên
                                        </Button>
                                    </Form.Item>
                                </div>

                            </Form>

                        </div>

                    </div>


                </div>
            </div> : false}
        </ConfigProvider>
        </>);
}