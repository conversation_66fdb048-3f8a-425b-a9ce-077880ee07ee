import { ApiUrl } from "@/ultils/apiUrl"
import NextAuth from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"


const authOptions: any = {
    // Configure one or more authentication providers
    secret: process.env.NEXTAUTH_SECRET,
    pages: {
        signIn: '/login',
        // signOut: '/signout',

    },
    session: { strategy: 'jwt' },
    callbacks: {
        async jwt({ token, user, account, profile, isNewUser }: { token: any, user: any, isNewUser: any, account: any, profile: any }) {
            
            if (user) {
                token.accessToken = user.access_token
            }
            return token

        },
        async session({ session, user, token }: { session: any, token: any, user: any }) {
            // Send properties to the client, like an access_token and user id from a provider
            session.accessToken = token.accessToken
            return session
        },
        async redirect({ url, baseUrl }: { url: any, baseUrl: any }) {
            // Allows relative callback URLs
            if (url.startsWith("/")) return `${baseUrl}${url}`
            // Allows callback URLs on the same origin
            else if (new URL(url).origin === baseUrl) return url
            return url
        },
    },

    // signOut: '/signout',
    providers: [
        CredentialsProvider({
            // The name to display on the sign in form (e.g. 'Sign in with...')
            name: 'Credentials',

            // The credentials is used to generate a suitable form on the sign in page.
            // You can specify whatever fields you are expecting to be submitted.
            // e.g. domain, username, password, 2FA token, etc.
            // You can pass any HTML attribute to the <input> tag through the object.
            credentials: {
                login: { label: "Tên đăng nhập", type: "text", placeholder: "Nhập tên đăng nhập (sđt/email/Mã số thuế)" },
                password: { label: "Mật khẩu", type: "************" }
            },


            async authorize(credentials, req) {
                // You need to provide your own logic here that takes the credentials
                // submitted and returns either a object representing a user or value
                // that is false/null if the credentials are invalid.
                // e.g. return { id: 1, name: 'J Smith', email: '<EMAIL>' }
                // You can also use the `req` object to obtain additional parameters
                // (i.e., the request IP address)
                console.log("123123123");
                console.log(`${process.env.LINK_API}/${ApiUrl.login}`)
                const res = await fetch(`${process.env.LINK_API}/${ApiUrl.login}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(credentials),
                })
                const user = await res.json()
                console.log(user);
                // If no error and we have user data, return it
                if (res.ok && user && user.result.status == 0) {
                    console.log("Đã vào")
                    return user.result.data
                }
                // Return null if user data could not be retrieved
                return null
            }
        })
        // ...add more providers here
    ],
}
const handler = NextAuth(authOptions)
export { handler as GET, handler as POST }