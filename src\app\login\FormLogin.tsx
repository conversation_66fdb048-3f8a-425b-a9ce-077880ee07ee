"use client"
import type { FormProps } from 'antd';
import { Button, Checkbox, Form, Input } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import Image from "next/image"; // nếu muốn t<PERSON>i <PERSON>u, còn không thì <img />
import Link from 'next/link';
import { useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react"

type FieldType = {
    login?: string;
    password?: string;
    remember?: string;
};

interface FormType {
    login: string,
    password: string
}



const onFinishFailed: FormProps<FieldType>['onFinishFailed'] = (errorInfo) => {
    console.log('Failed:', errorInfo);
};

const FormLogin = () => {
    const searchParams = useSearchParams();
    const onFinish: FormProps<FieldType>['onFinish'] = async (values) => {
        const login = values.login;
        const password = values.password;
        const callbackUrl = searchParams.get("callbackUrl") || "/";
        try {
            const res = await signIn('credentials', {
                login: login,
                password: password,
                // redirect: false,
                callbackUrl: callbackUrl
            })
            // console.log(res);
            // if (!res?.error) {
            //     const callBackUrl = '';
            //     router.push('/', { scroll: false });
            // } else {
            //     console.log(123123123);
            // }
        } catch (err) {
            console.log('Lỗi ');
        }
    }
    return (
        <>
            <Form
                name="basic"
                labelCol={{ span: 0 }}
                wrapperCol={{ span: 24 }}
                style={{ width: '100%', marginTop: 24 }}
                initialValues={{ remember: true }}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="off"
            >
                <Form.Item<FieldType>

                    rules={[{ required: true, message: 'Vui lòng nhập email!' }]}
                    label={null}
                    name={`login`}
                >
                    <Input size="large" style={{ height: 52 }} placeholder="Email của bạn" prefix={<Image
                        src="/images/icons/user.svg"
                        alt="user"
                        width={16}
                        height={16}
                    />} />

                </Form.Item>

                <Form.Item<FieldType>
                    rules={[{ required: true, message: 'Vui lòng nhập mật khẩu!' }]}
                    label={null}
                    name={`password`}
                >
                    <Input size="large" type='password' style={{ height: 52 }} placeholder="Mật khẩu" prefix={<Image
                        src="/images/icons/password.svg"
                        alt="user"
                        width={16}
                        height={16}
                    />} />

                </Form.Item>

                <div className='text-end mb-6 '>
                    <Link className='!text-primary' href={`/`} >Quên mật khẩu</Link>
                </div>



                <Form.Item label={null} >
                    <Button type="primary" htmlType="submit" className='w-full' style={{
                        paddingTop: 20,
                        paddingBottom: 20

                    }}>
                        Đăng nhập
                    </Button>
                </Form.Item>
            </Form>
        </>
    )
}

export default FormLogin;