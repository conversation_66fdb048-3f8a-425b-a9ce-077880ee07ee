"use client";
import React, { createContext, useContext } from "react";
import { notification } from "antd";

const NotificationContext = createContext<{ showError: (error: string) => void } | null>(null);

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [api, contextHolder] = notification.useNotification();

  const showError = (error: string) => {
    api.error({ message: error });
  };

  return (
    <NotificationContext.Provider value={{ showError }}>
      {contextHolder}
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotification() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error("useNotification must be used within a NotificationProvider");
  }
  return context;
}
