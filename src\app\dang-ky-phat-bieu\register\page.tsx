"use client"
import Image from "next/image";
import Link from "next/link";
import { redirect } from "next/navigation";

// import Content from "./content";

const DangKyPhatBieuPage = () => {
    return (
        <>

            <div className=" w-full ">
                <div className="flex justify-center mt-32">
                    <Image alt="speech" width={156} height={156} src={'/images/icons/speech-1.png'} />

                </div>
                <div className="flex justify-center mt-32">
                    <button onClick={() => {
                        redirect('/dang-ky-phat-bieu/waiting')
                    }} className="bg-primary px-12 py-4 text-white rounded-lg">
                        Đăng ký phát biểu
                    </button>
                </div>


            </div>

        </>
    );

}



export default DangKyPhatBieuPage;  