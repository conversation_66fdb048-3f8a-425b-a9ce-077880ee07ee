(function(r){const a=r["es-co"]=r["es-co"]||{};a.dictionary=Object.assign(a.dictionary||{},{"%0 of %1":"%0 de %1",Bold:"Negrita",Cancel:"Cancelar","Characters: %0":"Caracteres: %0",Italic:"<PERSON>ursiva",Save:"Guardar","Show more items":"Mostrar más elementos",Strikethrough:"Tachado",Underline:"Subrayado","Upload in progress":"Carga en progreso","Words: %0":"Palabras: %0"});a.getPluralForm=function(r){return r==1?0:r!=0&&r%1e6==0?1:2}})(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));