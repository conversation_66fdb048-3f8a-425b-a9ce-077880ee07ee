"use client";

import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Underline from "@tiptap/extension-underline";
import Heading from "@tiptap/extension-heading";
import TextAlign from "@tiptap/extension-text-align";
import { Placeholder } from '@tiptap/extensions'

import {
    BoldOutlined,
    ItalicOutlined,
    UnderlineOutlined,
    AlignLeftOutlined,
    AlignCenterOutlined,
    AlignRightOutlined,
    DownOutlined,
} from "@ant-design/icons";
import { Button, Dropdown, Menu, MenuProps } from "antd";
import { useEffect } from "react";
import { keymap } from "prosemirror-keymap";

import { selectAll } from "prosemirror-commands";

interface TiptapEditorProps {
    value?: string;
    onChange?: (value: string) => void;
}

const CustomSelectAll = keymap({
    "Mod-a": selectAll,
});

const TiptapEditor: React.FC<TiptapEditorProps> = ({ value, onChange }) => {
    const editor = useEditor({
        extensions: [
            StarterKit,
            Underline,
            Heading.configure({ levels: [1, 2, 3] }),
            TextAlign.configure({
                types: ["heading", "paragraph"],
                defaultAlignment: "left",
            }),
            Placeholder.configure({
                placeholder: 'Write something …',
            }),


        ],
        editorProps: {
            attributes: {
                class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
            },
        },
        content: value || "",
        immediatelyRender: false,
        onUpdate: ({ editor }) => {
            onChange?.(editor.getHTML());
        },
    });

    useEffect(() => {
        if (editor && value !== editor.getHTML()) {
            editor.commands.setContent(value || "");
        }
    }, [value]);

    if (!editor) return null;

    const headingMenu: MenuProps['items'] =
        [
            { key: "p", label: "Normal", onClick: () => editor.chain().focus().setParagraph().run() },
            { key: "h1", label: "Heading 1", onClick: () => editor.chain().focus().toggleHeading({ level: 1 }).run() },
            { key: "h2", label: "Heading 2", onClick: () => editor.chain().focus().toggleHeading({ level: 2 }).run() },
            { key: "h3", label: "Heading 3", onClick: () => editor.chain().focus().toggleHeading({ level: 3 }).run() },
        ];

    const alignMenu: MenuProps['items'] = [
        { key: "left", label: "", icon: <AlignLeftOutlined />, onClick: () => editor.chain().focus().setTextAlign("left").run() },
        { key: "center", label: "", icon: <AlignCenterOutlined />, onClick: () => editor.chain().focus().setTextAlign("center").run() },
        { key: "right", label: "", icon: <AlignRightOutlined />, onClick: () => editor.chain().focus().setTextAlign("right").run() },
    ];

    return (
        <div className="border border-[#D1D5DB] rounded-md">
            {/* Editor content */}
            <EditorContent
                editor={editor}
                placeholder="Nhập nội dung"
                className=" p-5 !focus:outline-none prose max-w-none"
            />

            {/* Toolbar giống hình bạn gửi */}
            <div className=" p-3 flex gap-2 items-center justify-center">
                {/* Heading Dropdown */}
                <Dropdown menu={{ items: headingMenu }} trigger={["click"]}>
                    <Button className="!bg-none !border-none" size="small">Aa <DownOutlined height={6} /></Button>
                </Dropdown>

                {/* Bold / Italic / Underline */}
                <Button
                    size="small"
                    type={editor.isActive("bold") ? "primary" : "text"}
                    icon={<BoldOutlined />}
                    onClick={() => editor.chain().focus().toggleBold().run()}
                />
                <Button
                    size="small"
                    type={editor.isActive("italic") ? "primary" : "text"}
                    icon={<ItalicOutlined />}
                    onClick={() => editor.chain().focus().toggleItalic().run()}
                />
                <Button
                    size="small"
                    type={editor.isActive("underline") ? "primary" : "text"}
                    icon={<UnderlineOutlined />}
                    onClick={() => editor.chain().focus().toggleUnderline().run()}
                />

                {/* Alignment Dropdown */}
                <Dropdown menu={{ items: alignMenu }} trigger={["click"]}>
                    <Button className="!bg-none !border-none" size="small">☰ <DownOutlined height={6} /></Button>
                </Dropdown>
            </div>
        </div>
    );
};

export default TiptapEditor;
