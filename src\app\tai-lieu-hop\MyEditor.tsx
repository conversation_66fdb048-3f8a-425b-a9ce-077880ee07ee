// components/custom-editor.js
'use client' // Required only in App Router.

import React, { useEffect, useState } from 'react';
import { CKEditor, useCKEditorCloud } from '@ckeditor/ckeditor5-react';
import ClassicEditor from "@ckeditor/ckeditor5-build-classic";

interface MyEditorProps {
    value?: string;
    onChange?: (value: string) => void;
}

const CustomEditor = ({ value, onChange }: MyEditorProps) => {
    const [data, setData] = useState(value || "");

    useEffect(() => {
        setData(value || "");
    }, [value]);

    return (
        <CKEditor
            editor={ClassicEditor}
            data={data}
            config={{
                ui: {
                    toolbar: {
                        location: 'bottom'
                    }
                },
                toolbar: {
                    location: 'bottom',
                    items: ["heading", "|", "bold", "italic", "underline", "|", "alignment"],


                },
                locationbar: 'bottom'
            }}
            onChange={(_: any, editor: { getData: () => any; }) => {
                const newData = editor.getData();
                setData(newData);
                onChange?.(newData);
            }}
        />
    );
};

export default CustomEditor;
