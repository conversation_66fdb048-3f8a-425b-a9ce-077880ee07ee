import { notification } from "antd";

export function showError(error: string) {
    notification.error({ message: error });
}

/** <PERSON>yển đổi key từ camelCase sang snake_case */
export function mapRequestParams(params: Record<string, any>, keyMap: Record<string, string>): Record<string, any> {
    if (!params) return {};

    return Object.keys(params).reduce((acc, key) => {
        const newKey = keyMap[key] || key; // Nếu có keyMap thì đổi, không có thì giữ nguyên
        acc[newKey] = params[key];
        return acc;
    }, {} as Record<string, any>);
}

