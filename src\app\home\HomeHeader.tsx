"use client"

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

export default function HomeHeader() {
    const [diemDanh, setDiemDanh] = useState<boolean>(false);
    const [isDiemDanh, setIsDiemDanh] = useState<boolean>(false);

    const clickConfirm = () => {
        setDiemDanh(true);
        setIsDiemDanh(false);
    }

    return <div className="text-white">
        <div className="flex justify-between p-5 items-center">
            <Link href={`/thu-moi-du-hop`} >
                <Image className="inline" alt="Back" width={28} height={28} src={'/images/icons/Back.png'} />
                Quay lại
            </Link>

            <Image alt="Logo" src={`/images/logo.png`} width={60} height={0} sizes="100%" priority />

            <Link href={``} >
                <Image alt="search" width={28} height={0} sizes="100%" priority src={'/images/icons/search-normal.png'} />
            </Link>

        </div>

        <div className="flex justify-between p-5 items-center">
            <div>
                <p className="font-extralight">Chào đồng chí 👋</p>
                <p className="font-medium">Nguyễn Thị Trâm Anh</p>
            </div>
            <div>
                {!diemDanh ? <button className="text-black bg-[#FFBD26] px-12 py-3 rounded-lg " onClick={() => setIsDiemDanh(true)}>
                    Điểm danh
                </button> : <button className="text-black bg-white px-4 py-3 rounded-lg " >
                    <Image className="inline" alt="Check" width={20} height={20} src={'/images/icons/tick-circle.png'} />
                    <span className="ms-2">Đã điểm danh</span>
                </button>}
            </div>
        </div>

        {isDiemDanh ? <div className="fixed w-screen h-screen top-0 left-0 bg-black/50 text-black">
            <div className="flex w-full h-full justify-center items-center ">
                <div className="w-[500px]  bg-white  px-10 py-8 rounded-3xl text-center relative">
                    <div className="">
                        <div className="flex justify-center">
                            <Image alt="Logo" src={`/images/icons/Illustration-profile.png`} width={0} className="w-[90px]" height={0} sizes="100%" priority />
                        </div>
                        <h3 className="font-bold text-[20px] my-4">
                            Xác nhận Điểm danh?
                        </h3>
                        <p className="px-6">
                            Bạn có chắc chắn muốn điểm danh không
                            Hệ thống sẽ ghi nhận
                        </p>
                        <div className="grid grid-cols-2 gap-3">
                            <button onClick={() => setIsDiemDanh(false)} className="bg-danger w-full text-white py-3 mt-4 rounded-lg cursor-pointer">Hủy</button>
                            <button onClick={clickConfirm} className="bg-primary w-full text-white py-3 mt-4 rounded-lg cursor-pointer">Xác nhận</button>

                        </div>
                    </div>
                    <div className="absolute z-1 top-[15px] right-[15px]">
                        <button onClick={() => setIsDiemDanh(false)}>
                            <Image alt="Logo" src={`/images/icons/close.png`} width={28} height={28} />

                        </button>
                    </div>
                </div>


            </div>
        </div> : false}
    </div>
}