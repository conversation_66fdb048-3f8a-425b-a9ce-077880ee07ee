"use client"
import { useLichHop } from "@/hook/hook";
import Image from "next/image";
import HeaderCalendar from "@/components/HeaderCalendar";

export default function Home() {
  const { data, isLoading, isError } = useLichHop();

  if (data) {
    console.log(data)
    return (
      <div className="bg-gradient-to-r from-[#014A3F] to-[#41754F] h-full w-full flex flex-col">
        <HeaderCalendar />
      </div>
    );
  }

}
