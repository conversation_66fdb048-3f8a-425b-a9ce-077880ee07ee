"use client"
import Image from "next/image";
import Link from "next/link";
import { redirect } from "next/navigation";
import { useState } from "react";

// import Content from "./content";

const DangKyPhatBieuPage = () => {
    const [isRegister, setIsRegister] = useState<boolean>(false)
    return (
        <>

            <div className=" w-full ">
                <div className="flex justify-center mt-32">
                    <Image alt="speech" width={156} height={156} src={'/images/icons/speech-1.png'} />

                </div>
                <div className="text-center mt-8">
                    <p>Bạn đang chờ phát biểu với số thứ tự</p>
                    <h3 className="font-[700] text-[24px]">05</h3>
                </div>
                <div className="flex justify-center mt-8">
                    <button onClick={() => setIsRegister(true)} className="bg-danger px-12 py-4 text-white rounded-lg">
                        H<PERSON><PERSON> đăng ký
                    </button>
                </div>


            </div>

            {isRegister ? <div className="fixed w-screen h-screen top-0 left-0 bg-black/50 text-black">
                <div className="flex w-full h-full justify-center items-center ">
                    <div className="w-[500px]  bg-white  px-10 py-8 rounded-3xl text-center relative">
                        <div className="">
                            <div className="flex justify-center">
                                <Image alt="Logo" src={`/images/icons/Illustration-shape.png`} width={0} className="w-[90px]" height={0} sizes="100%" priority />
                            </div>
                            <h3 className="font-bold text-[20px] my-4">
                                Huỷ đăng ký phát biểu
                            </h3>
                            <p className="px-6">
                                Bạn chắc chắn muốn huỷ đăng ký phát biểu?
                                Hệ thống sẽ ghi nhận
                            </p>
                            <div className="w-full grid grid-cols-2 gap-4 mt-3">
                                <button onClick={() => setIsRegister(false)} className="bg-[#D1D5DB] text-black w-full py-3 mt-4 rounded-lg cursor-pointer">Hủy bỏ</button>
                                <button onClick={() => {
                                    setIsRegister(false);
                                    redirect('/dang-ky-phat-bieu/register')
                                }} className="bg-primary w-full  text-white py-3 mt-4 rounded-lg cursor-pointer">Đồng ý</button>

                            </div>
                        </div>
                        <div className="absolute z-1 top-[15px] right-[15px]">
                            <button onClick={() => setIsRegister(false)}>
                                <Image alt="Logo" src={`/images/icons/close.png`} width={28} height={28} />

                            </button>
                        </div>
                    </div>


                </div>
            </div> : false}

        </>
    );

}



export default DangKyPhatBieuPage;  