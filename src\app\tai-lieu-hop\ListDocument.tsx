import { Button, Form, FormProps, Input } from "antd";
import Dragger from "antd/es/upload/Dragger";
import Image from "next/image";
import MyEditor from "./MyEditor";
import CustomEditor from "./MyEditor";
import ClientSideCustomEditor from "./client-side-custom-editor";
import TiptapEditor from "./TiptapEditor";
import { useState } from "react";

type FieldType = {
    username?: string;
    password?: string;
    remember?: string;
};



const onFinishFailed: FormProps<FieldType>['onFinishFailed'] = (errorInfo) => {
    console.log('Failed:', errorInfo);

};

export default function ListDocument() {
    const [form] = Form.useForm();
    const [isFormSubmit, SetIsFormSubmit] = useState<boolean>(false)
    const [isFormSubmitNoti, SetIsFormSubmitNoti] = useState<boolean>(false);
    const [isConfirmDelete, setIsConfirmDelete] = useState<boolean>(false)
    const [isConfirmDeleteNoti, setIsConfirmDeleteNoti] = useState<boolean>(false)

    const onFinish: FormProps<FieldType>['onFinish'] = (values) => {
        console.log('Success:', values);
        SetIsFormSubmitNoti(true)
    };

    return (<>
        <div className="">
            <div className="flex gap-3 items-center border-b-1 border-[#E5E7EB] py-8">
                <div className="flex-1 ">
                    <div className="flex justify-start items-center">
                        <Image className="w-[35px]" sizes="100%" priority alt="Back" width={0} height={0} src={`/images/icons/pdf_icon.png`} />
                        <div className="ms-3">
                            <p>Tài liệu chuyển đổi số - Biên bản họp kế hoạch tháng 10</p>
                            <p className="font-extralight" >20/08/2025</p>
                        </div>
                    </div>
                </div>
                <div className="flex">
                    <button className="bg-primary px-4 rounded-lg text-white py-2 me-2 flex items-center"><Image alt="Download" className="inline me-1" width={16} height={16} src={`/images/icons/download.png`} /> Tải về</button>
                    <button onClick={() => SetIsFormSubmit(true)} className="bg-secondary px-4 rounded-lg text-white py-2 flex items-center"><Image alt="Download" className="inline me-1" width={16} height={16} src={`/images/icons/messages-2.png`} /> Góp ý</button>
                </div>
            </div>
            <div className="flex gap-3 items-center border-b-1 border-[#E5E7EB] py-8">
                <div className="flex-1 ">
                    <div className="flex justify-start items-center">
                        <Image className="w-[35px]" sizes="100%" priority alt="Back" width={0} height={0} src={`/images/icons/word_icon.png`} />
                        <div className="ms-3">
                            <p>Tài liệu chuyển đổi số - Biên bản họp kế hoạch tháng 10</p>
                            <p className="font-extralight">20/08/2025</p>
                        </div>
                    </div>
                </div>
                <div className="flex">
                    <button className="bg-primary px-4 rounded-lg text-white py-2 me-2 flex items-center"><Image alt="Download" className="inline me-1" width={16} height={16} src={`/images/icons/download.png`} /> Tải về</button>
                    <button onClick={() => SetIsFormSubmit(true)} className="bg-secondary px-4 rounded-lg text-white py-2 flex items-center"><Image alt="Download" className="inline me-1" width={16} height={16} src={`/images/icons/messages-2.png`} /> Góp ý</button>
                </div>
            </div>
            <div className="flex gap-3 items-center border-b-1 border-[#E5E7EB] py-8">
                <div className="flex-1 ">
                    <div className="flex justify-start items-center">
                        <Image className="w-[35px]" sizes="100%" priority alt="Back" width={0} height={0} src={`/images/icons/excel_icon.png`} />
                        <div className="ms-3">
                            <p>Tài liệu chuyển đổi số - Biên bản họp kế hoạch tháng 10</p>
                            <p className="font-extralight">20/08/2025</p>
                        </div>
                    </div>
                </div>
                <div className="flex">
                    <button className="bg-primary px-4 rounded-lg text-white py-2 me-2 flex items-center"><Image alt="Download" className="inline me-1" width={16} height={16} src={`/images/icons/download.png`} /> Tải về</button>
                    <button onClick={() => SetIsFormSubmit(true)} className="bg-secondary px-4 rounded-lg text-white py-2 flex items-center"><Image alt="Download" className="inline me-1" width={16} height={16} src={`/images/icons/messages-2.png`} /> Góp ý</button>
                </div>
            </div>
            <div className="flex gap-3 items-center border-b-1 border-[#E5E7EB] py-8">
                <div className="flex-1 ">
                    <div className="flex justify-start items-center">
                        <Image className="w-[35px]" sizes="100%" priority alt="Back" width={0} height={0} src={`/images/icons/zip_icon.png`} />
                        <div className="ms-3">
                            <p className="font-extralight">Tài liệu chuyển đổi số - Biên bản họp kế hoạch tháng 10</p>
                            <p className="font-extralight text-danger">chờ duyệt</p>
                        </div>
                    </div>
                </div>
                <div className="flex">
                    <button onClick={() => setIsConfirmDelete(true)} className="bg-danger px-4 rounded-lg text-white py-2 flex items-center"><Image alt="Download" className="inline me-1" width={16} height={16} src={`/images/icons/trash.png`} /> Xóa</button>
                </div>
            </div>

        </div>

        {isFormSubmit ? <div className="fixed w-screen h-screen top-0 left-0 bg-black/50 text-black">
            <div className="flex w-full h-full justify-center items-center ">
                <div className="w-[550px]  bg-white  px-6 py-6 rounded-3xl text-center relative">
                    <div className="">
                        <div className='relative '>

                            <h3 className="font-bold text-center text-[20px] my-4">
                                Tải lên tài liệu cuộc họp
                            </h3>
                            <div className="absolute top-0 right-0">
                                <button onClick={() => SetIsFormSubmit(false)}>
                                    <Image alt="Logo" src={`/images/icons/close.png`} width={28} height={28} />

                                </button>
                            </div>
                        </div>

                        <Form
                            name="basic"
                            layout='vertical'
                            style={{ width: '100%', marginTop: 24 }}
                            initialValues={{ remember: true }}
                            onFinish={onFinish}
                            onFinishFailed={onFinishFailed}
                            autoComplete="off"
                            form={form}
                        >
                            <Form.Item

                                rules={[{ required: true, message: 'Vui lòng nhập nội dung góp ý!' }]}
                                label={`Nội dung góp ý`}
                                name="content"
                            >
                                {/* <Input size="large" style={{ height: 52 }} placeholder="Nhập tên tài liệu" /> */}
                                <TiptapEditor />
                            </Form.Item>






                            <div className='grid grid-cols-2 gap-5'>
                                {/* <Form.Item label={null} > */}
                                <Button onClick={() => SetIsFormSubmit(false)} type="primary" className='w-full !text-black !bg-[#D1D5DB]' style={{
                                    paddingTop: 30,
                                    paddingBottom: 30,
                                    fontWeight: 'bold'

                                }}>
                                    Hủy bỏ
                                </Button>
                                {/* </Form.Item> */}
                                <Form.Item label={null} >
                                    <Button type="primary" htmlType="submit" className='w-full' style={{
                                        paddingTop: 30,
                                        paddingBottom: 30,
                                        fontWeight: 'bold'

                                    }}>
                                        Tải lên
                                    </Button>
                                </Form.Item>
                            </div>

                        </Form>

                    </div>

                </div>


            </div>
        </div> : false}


        {isFormSubmitNoti ? <div className="fixed w-screen h-screen top-0 left-0 bg-black/50 text-black">
            <div className="flex w-full h-full justify-center items-center ">
                <div className="w-[500px]  bg-white  px-10 py-8 rounded-3xl text-center relative">
                    <div className="">
                        <div className="flex justify-center">
                            <Image alt="Logo" src={`/images/icons/Illustration-done.png`} width={0} className="w-[90px]" height={0} sizes="100%" priority />
                        </div>
                        <h3 className="font-bold text-[20px] my-4">
                            Đã ghi nhận góp ý
                        </h3>
                        <p className="px-6">
                            Hệ thống đã ghi nhận góp ý của bạn thành công
                        </p>
                        <button onClick={() => {
                            SetIsFormSubmitNoti(false)
                            SetIsFormSubmit(false)
                        }} className="bg-primary px-20 text-white py-3 mt-4 rounded-lg cursor-pointer">Xong</button>
                    </div>
                    <div className="absolute z-1 top-[15px] right-[15px]">
                        <button onClick={() => {
                            SetIsFormSubmitNoti(false)
                        }}>
                            <Image alt="Logo" src={`/images/icons/close.png`} width={28} height={28} />

                        </button>
                    </div>
                </div>


            </div>
        </div> : false}

        {isConfirmDelete ? <div className="fixed w-screen h-screen top-0 left-0 bg-black/50 text-black">
            <div className="flex w-full h-full justify-center items-center ">
                <div className="w-[500px]  bg-white  px-10 py-8 rounded-3xl text-center relative">
                    <div className="">
                        <div className="flex justify-center">
                            <Image alt="Logo" src={`/images/icons/Illustration-delete.png`} width={0} className="w-[90px]" height={0} sizes="100%" priority />
                        </div>
                        <h3 className="font-bold text-[20px] my-4">
                            Bạn chắc chắn muốn xóa?
                        </h3>
                        <p className="px-6">
                            Bạn có chắc chắn muốn xoá tài liệu này không
                            Hành động này không thể hoàn tác
                        </p>
                        <div className="w-full grid grid-cols-2 gap-4">
                            <button onClick={() => {
                                setIsConfirmDelete(false)
                            }} className="bg-[#D1D5DB] text-black w-full py-3 mt-4 rounded-lg cursor-pointer">Hủy bỏ</button>
                            <button onClick={() => {
                                setIsConfirmDelete(false)
                                setIsConfirmDeleteNoti(true)
                            }} className="bg-primary w-full  text-white py-3 mt-4 rounded-lg cursor-pointer">Xác nhận</button>

                        </div>

                    </div>
                    <div className="absolute z-1 top-[15px] right-[15px]">
                        <button onClick={() => {
                            setIsConfirmDelete(false)
                        }}>
                            <Image alt="Logo" src={`/images/icons/close.png`} width={28} height={28} />

                        </button>
                    </div>
                </div>


            </div>
        </div> : false}

        {isConfirmDeleteNoti ? <div className="fixed w-screen h-screen top-0 left-0 bg-black/50 text-black">
            <div className="flex w-full h-full justify-center items-center ">
                <div className="w-[500px]  bg-white  px-10 py-8 rounded-3xl text-center relative">
                    <div className="">
                        <div className="flex justify-center">
                            <Image alt="Logo" src={`/images/icons/Illustration-done.png`} width={0} className="w-[90px]" height={0} sizes="100%" priority />
                        </div>
                        <h3 className="font-bold text-[20px] my-4">
                            Đã ghi nhận góp ý
                        </h3>
                        <p className="px-6">
                            Hệ thống đã ghi nhận góp ý của bạn thành công
                        </p>
                        <button onClick={() => {
                            setIsConfirmDeleteNoti(false)
                        }} className="bg-primary px-20 text-white py-3 mt-4 rounded-lg cursor-pointer">Xong</button>
                    </div>
                    <div className="absolute z-1 top-[15px] right-[15px]">
                        <button onClick={() => {
                            setIsConfirmDeleteNoti(false)
                        }}>
                            <Image alt="Logo" src={`/images/icons/close.png`} width={28} height={28} />

                        </button>
                    </div>
                </div>


            </div>
        </div> : false}

    </>);
}

