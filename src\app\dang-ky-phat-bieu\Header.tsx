"use client"

import Link from "next/link"
import Image from "next/image"

export default function Header() {
    return <div className="flex justify-between p-5 items-center border-b-1 border-[#D1D5DB]">
        <Link href={`/home`} >
            <Image className="inline" alt="Back" width={28} height={28} src={'/images/icons/Back-dark.png'} />
            Quay lại
        </Link>

        <h2 className="font-[600] ">Đăng ký phát biểu</h2>

        <div className="flex justify-between gap-6 items-center">
            <Link href={``} >
                <Image alt="search" width={28} height={0} sizes="100%" priority src={'/images/icons/search-normal-dark.png'} />
            </Link>

            <button className="rounded-full overflow-hidden">
                <Image alt="search" width={36} height={36} src={'/images/icons/avatar.jpg'} />

            </button>
        </div>


    </div>
}