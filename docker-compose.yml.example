version: "3"

services:
  nextapp:
    container_name: nextapp
    # image: hoangthongblog/nextapp
    volumes:
      - ./:/app
      - /app/.next
      - /app/node_modules
    # command: npm run build
    build:
      context: .
      dockerfile: Dockerfile
    # env_file:
    #   - .env.prod
    
    ports:
      - 3000:3000
    restart: always
#   nginx:
#     image: nginx:latest
#     container_name: nginx-proxy
#     ports:
#       - "8000:8000"
#     # volumes:
#     #   - ./nginx.conf:/etc/nginx/nginx.conf
#     volumes:
#       - ./start_nginx.sh:/start_nginx.sh
#     environment:
#       - MIN_RTP_PORT=40000
#       - MAX_RTP_PORT=40020
#     entrypoint: /bin/bash /start_nginx.sh
#     depends_on:
#       - nextapp
# networks:
#   default:
#     driver: bridge