@import "tailwindcss";

@theme {
  --color-primary: #046C4E;
  /* ví dụ custom biến color */
  --color-danger: #E02424;
  /* ví dụ custom biến color */
  --color-secondary: #EB9100;
  /* ví dụ custom biến color */
  --boxShadow-item-home: 0px 2px 4px -2px rgba(0, 0, 0, 0.05), 0px 4px 6px -1px rgba(0, 0, 0, 0.1)
    /* Bạn có thể thêm biến cho font, breakpoint, gradient, ... */
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-size: 16px;
  /* font-family: Arial, Helvetica, sans-serif; */
}

.ant-tabs-tab {
  padding: 25px 0 !important;
  font-weight: bold;
}

.ant-tabs-nav {
  padding: 0 25px !important;
  background: #F3F4F6;
}

.ProseMirror {
  min-height: 150px;
  @apply select-text; /* tailwind: cho phép chọn chữ */
  /* Minimum height */
}

.ant-tabs-content {
  padding: 0 25px !important;
}

.ProseMirror h1 {
  @apply text-2xl font-bold;
}

.ProseMirror h2 {
  @apply text-xl font-semibold;
}

.ProseMirror h3 {
  @apply text-lg font-medium;
}

.ProseMirror p {
  @apply text-base;
}