import FrameBox from "@/components/FrameBox";
import Image from "next/image";
import FormLogin from "./FormLogin";

const LoginPage = () => {
    return (
        <>
            <div className="bg-[url(/images/background.png)] w-full h-full bg-no-repeat bg-cover flex items-center justify-center bg-bottom-right" >
                <div className="text-center text-[#111928] max-w-[600px] w-3/5">
                    <FrameBox>
                        <div className="flex justify-center">
                            <Image alt="Logo" src={`/images/logo.png`} width={156} height={0} sizes="100%" priority />

                        </div>
                        <h2 className="text-[26px] my-[15px] font-[700]">Đăng nhập</h2>
                        <p className="font-[600]">Đăng nhập bằng email hoặc số điện thoại</p>
                        
                        <FormLogin />

                    </FrameBox>
                </div>
            </div>
        </>
    );

}

export default LoginPage;  