"use client";
import { useState } from "react";
import dayjs from "dayjs";

const HeaderCalendar = () => {
  const [selectedDate, setSelectedDate] = useState(dayjs());

  // L<PERSON>y tuần hiện tạ<PERSON> (Mon -> Sun)
  const startOfWeek = selectedDate.startOf("week").add(1, "day"); // bắt đầu từ Thứ 2
  const days = Array.from({ length: 7 }, (_, i) => startOfWeek.add(i, "day"));

  return (
    <div className=" text-white p-4 ">
      {/* Tháng - Năm */}
      <div className="flex justify-center items-center mb-4 ">

        <div>
          <button onClick={() => setSelectedDate(selectedDate.subtract(1, "week"))}>
            ◀
          </button>
          <span className="text-lg font-semibold">
            Tháng {selectedDate.month() + 1}, {selectedDate.year()}
          </span>
          <button onClick={() => setSelectedDate(selectedDate.add(1, "week"))}>
            ▶
          </button>
        </div>

      </div>

      {/* D<PERSON>y ngày trong tuần */}
      <div className="flex justify-between px-20">
        {days.map((day) => {
          const isToday = day.isSame(dayjs(), "day");
          const isSelected = day.isSame(selectedDate, "day");

          return (
            <div className="text-center" key={day.format("YYYY-MM-DD")}>
              <div className="text-sm mb-3 text-center text[#D1D5DB] font-[400] text-[14px]">{day.format("ddd")}</div>
              <div

                className={`flex  w-[50px] h-[50px] justify-center items-center  cursor-pointer  rounded-full transition ${isSelected
                  ? "bg-orange-500 text-white"

                  : "hover:bg-green-700"
                  }`}
                onClick={() => setSelectedDate(day)}
              >

                <span className="font-[600] text-[20px] p-0 leading-0">{day.format("DD")}</span>
              </div>

            </div>

          );
        })}
      </div>
    </div>
  );
};

export default HeaderCalendar;
