"use client"

import Header from "../Header";
import { useEffect, useState } from "react";

type Seat = {
    id: string;
    label: string;
    selected?: boolean;
};

const ViTriNgoi = () => {
    const [leftSeats, setLeftSeats] = useState<Seat[]>([]);
    const [rightSeats, setRightSeats] = useState<Seat[]>([]);
    const [bottomSeats, setBottomSeats] = useState<Seat[]>([]);

    // Giả lập gọi API
    useEffect(() => {
        // Ví dụ response API
        const left = Array.from({ length: 10 }, (_, i) => ({
            id: `L${i}`,
            label: "R1C1",
            selected: i === 1, // mark seat thứ 2 selected
        }));
        const right = Array.from({ length: 10 }, (_, i) => ({
            id: `R${i}`,
            label: "R1C1",
        }));
        const bottom = Array.from({ length: 8 }, (_, i) => ({
            id: `B${i}`,
            label: "R1C1",
        }));

        setLeftSeats(left);
        setRightSeats(right);
        setBottomSeats(bottom);
    }, []);

    return (
        <>
            <div className="bg-[#F3F4F6] h-full w-full text-black">
                <Header />
                <div className=" w-full px-8 py-10">
                    <h2 className="font-[700] text-[18px] text-center ">Vị trí ngồi bạn: <span className="text-primary">R1C1-A</span></h2>
                    <div className="mx-5 my-8 px-25 py-8 bg-white rounded-lg">
                        <p className="text-center font-[500] text-[18px]">Sơ đồ vị trí ngồi</p>
                        <div className="grid grid-cols-7 gap-3 my-8">
                            <div className="col-span-2">
                                <button className="text-white bg-[#FF5A1F] w-full py-3 rounded-lg">Thư ký</button>
                            </div>
                            <div className="col-span-3">
                                <button className="text-white bg-danger w-full py-3 rounded-lg">Chủ tọa ký họp</button>
                            </div>
                            <div className="col-span-2">
                                <button className="text-white bg-[#FF5A1F] w-full py-3 rounded-lg">Bục phát biểu</button>
                            </div>
                        </div>

                        <div className="grid grid-cols-8 gap-3 justify-start">
                            <div className="grid grid-cols-2 gap-3 col-span-2">
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>

                            </div>
                            <div className="col-span-4 px-12 mb-10">
                                <div className="bg-[#D1D5DB] w-full h-full rounded-lg">

                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-3 col-span-2">
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>
                                <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                    R1C1
                                </button>


                                
                            </div>



                        </div>

                        <div className="grid grid-cols-8 gap-3 mt-3">
                            <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                R1C1
                            </button>
                            <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                R1C1
                            </button>
                            <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                R1C1
                            </button>
                            <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                R1C1
                            </button>
                            <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                R1C1
                            </button>
                            <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                R1C1
                            </button>
                            <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                R1C1
                            </button>
                            <button className="hover:bg-primary hover:text-white focus:bg-primary focus:text-white bg-[#F3F4F6] border border-[#D1D5DB] rounded-lg py-3 w-full font-medium">
                                R1C1
                            </button>
                        </div>

                    </div>
                </div>
            </div>
        </>
    );

}

export default ViTriNgoi;  