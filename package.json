{"name": "p<PERSON><PERSON>c", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@ant-design/nextjs-registry": "^1.1.0", "@ckeditor/ckeditor5-build-classic": "file:custom_modules/ckeditor5-build-classic-custom", "@ckeditor/ckeditor5-react": "^5.0.5", "@tiptap/extension-heading": "^3.2.1", "@tiptap/extension-text-align": "^3.2.1", "@tiptap/extension-underline": "^3.2.1", "@tiptap/react": "^3.2.1", "@tiptap/starter-kit": "^3.2.1", "antd": "^5.27.1", "next": "15.5.0", "next-auth": "^4.24.11", "react": "^18.3.1", "react-dom": "^18.3.1", "swr": "2.3.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.5.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "^5"}}