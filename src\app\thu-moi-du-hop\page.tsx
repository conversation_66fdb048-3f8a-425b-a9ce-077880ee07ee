"use client"

import FrameBox from "@/components/FrameBox";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { redirect, usePathname } from 'next/navigation'

const ThuMoiDuHopPage = () => {
    const [thamGia, setThamGia] = useState<boolean>(false)

    const clickThamGia = () => {
        setThamGia(true);
    }

    const clickFinish = () => {
       redirect('/home')
    }

    const closeThamGia = () => {
        setThamGia(false)
    }

    return (
        <>
            <div className="bg-[url(/images/background.png)] w-full h-full bg-no-repeat bg-cover flex items-center justify-center bg-bottom-right" >
                <div className=" text-[#111928] w-3/5 max-w-[600px]">
                    <FrameBox>
                        <div className="flex justify-center">
                            <Image alt="Logo" src={`/images/logo.png`} width={156} height={0} sizes="100%" priority />

                        </div>
                        <h2 className="text-[26px] my-[15px] font-[700] text-center"><PERSON>h<PERSON> mời dự họp</h2>


                        <div className="w-full">
                            <p>Kính gửi, <b>Đồng chí Nguyễn Thị Trâm Anh</b></p>
                            <p>Phòng Nội vụ kính mời Đồng chí tham gia vào buổi họp như sau:</p>

                            <div className="bg-gray-100/80 p-3 mt-4 rounded-lg grid grid-cols-8 gap-4">
                                <div className="col-span-2">Nội dung:</div>
                                <div className="col-span-6 font-medium">Triển khai kế hoạch Tháng 10 và Đại hội Đảng các cấp - Năm 2025</div>
                                <div className="h-[1px] bg-[#E5E7EB] w-full col-span-8 gap-0"></div>
                                <div className="col-span-2">Chủ trì:</div>
                                <div className="col-span-6 font-medium">Trưởng ban pháp chế Cao Thế Nghĩa</div>
                                <div className="h-[1px] bg-[#E5E7EB] w-full col-span-8 gap-0"></div>

                                <div className="col-span-2">Thời gian:</div>
                                <div className="col-span-6 font-medium">08:30 - 12:00, Ngày 26/08/2025</div>
                                <div className="h-[1px] bg-[#E5E7EB] w-full col-span-8 gap-0"></div>

                                <div className="col-span-2">Địa điểm:</div>
                                <div className="col-span-6 font-medium">Phòng số 2 - Bộ Công An</div>
                            </div>
                            <p className="my-5 text-center font-medium">Vui lòng xác nhận</p>
                            <div className="grid grid-cols-3 gap-2">
                                <button onClick={clickThamGia} className="flex justify-center cursor-pointer items-center w-full h-[52px] bg-[#0E9F6E] rounded-lg text-white font-medium">Tham gia</button>
                                <button className="flex justify-center cursor-pointer items-center w-full h-[52px] bg-[#E02424] rounded-lg text-white font-medium">Từ chối</button>
                                <button className="flex justify-center cursor-pointer items-center w-full h-[52px] bg-[#1C64F2] rounded-lg text-white font-medium">Chưa chắc chắn</button>
                            </div>
                        </div>




                    </FrameBox>
                </div>


            </div>

            {thamGia ? <div className="fixed w-screen h-screen top-0 left-0 bg-black/50 text-black">
                <div className="flex w-full h-full justify-center items-center ">
                    <div className="w-[500px]  bg-white  px-10 py-8 rounded-3xl text-center relative">
                        <div className="">
                            <div className="flex justify-center">
                                <Image alt="Logo" src={`/images/icons/Illustration.png`} width={0} className="w-[90px]" height={0} sizes="100%" priority />
                            </div>
                            <h3 className="font-bold text-[20px] my-4">
                                Xác nhận thành công
                            </h3>
                            <p className="px-6">
                                Cảm ơn bạn đã xác nhận tham gia phiên họp
                                của phòng Nội vụ
                            </p>
                            <button onClick={clickFinish} className="bg-primary w-full text-white py-3 mt-4 rounded-lg cursor-pointer">Xong</button>
                        </div>
                        <div className="absolute z-1 top-[15px] right-[15px]">
                            <button onClick={closeThamGia}>
                                <Image alt="Logo" src={`/images/icons/close.png`} width={28} height={28} />

                            </button>
                        </div>
                    </div>


                </div>
            </div> : false}
        </>
    );

}

export default ThuMoiDuHopPage;  